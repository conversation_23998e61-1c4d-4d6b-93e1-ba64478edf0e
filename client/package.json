{"name": "emma-client", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@blueprintjs/core": "^5.19.0", "@blueprintjs/icons": "^5.22.0", "@blueprintjs/popover2": "^2.1.31", "@blueprintjs/select": "^5.3.20", "@chakra-ui/react": "^3.17.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@google/generative-ai": "^0.24.1", "@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@lexical/code": "^0.32.1", "@lexical/headless": "^0.32.1", "@lexical/history": "^0.32.1", "@lexical/html": "^0.32.1", "@lexical/link": "^0.32.1", "@lexical/list": "^0.32.1", "@lexical/mark": "^0.32.1", "@lexical/markdown": "^0.32.1", "@lexical/overflow": "^0.32.1", "@lexical/plain-text": "^0.32.1", "@lexical/react": "^0.32.1", "@lexical/rich-text": "^0.32.1", "@lexical/selection": "^0.32.1", "@lexical/table": "^0.32.1", "@lexical/utils": "^0.32.1", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-context-menu": "^2.2.12", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-hover-card": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.12", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.4", "@supabase/supabase-js": "^2.49.9", "@tabler/icons-react": "^3.34.0", "@tanstack/react-query": "^5.74.11", "@tldraw/tldraw": "^2.4.0", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "color-namer": "^1.4.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "etherpad-lite-client": "^0.9.0", "fabric": "^6.6.4", "firebase": "^11.6.1", "framer-motion": "^12.9.2", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "immer": "^10.1.1", "input-otp": "^1.4.2", "jspdf": "^3.0.1", "konva": "^9.3.20", "lexical": "^0.32.1", "lucide-react": "^0.503.0", "mobx": "^6.13.7", "mobx-react-lite": "^4.1.0", "polotno": "^2.23.9", "postcss": "^8.5.3", "prop-types": "^15.8.1", "react": "^18.2.0", "react-colorful": "^5.6.1", "react-day-picker": "^9.6.7", "react-dom": "^18.2.0", "react-hook-form": "^7.56.1", "react-konva": "^18.2.10", "react-konva-utils": "^1.1.0", "react-markdown": "^10.1.0", "react-player": "^2.16.0", "react-resizable-panels": "^2.1.9", "react-router-dom": "^6.30.0", "recharts": "^2.15.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "use-image": "^1.1.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "wouter": "^3.7.0", "zod": "^3.24.3", "zustand": "^5.0.5"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vitejs/plugin-react": "^4.4.1", "depcheck": "^1.4.7", "eslint": "^9.26.0", "tailwindcss": "^3.4.17", "typescript": "^5.0.0", "vite": "^5.4.18"}}