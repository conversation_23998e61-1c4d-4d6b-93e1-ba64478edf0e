/**
 * Simple test component to verify Lexical editor functionality
 */

import React, { useState } from 'react';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { HeadingNode } from '@lexical/rich-text';
import { ListNode, ListItemNode } from '@lexical/list';
import { LinkNode } from '@lexical/link';
import { $getRoot } from 'lexical';

const LexicalEditorTest: React.FC = () => {
  const [content, setContent] = useState('');

  const initialConfig = {
    namespace: 'LexicalEditorTest',
    theme: {
      text: {
        bold: 'font-bold',
        italic: 'italic',
        underline: 'underline',
      },
      paragraph: 'mb-2',
    },
    nodes: [HeadingNode, ListNode, ListItemNode, LinkNode],
    onError: (error: Error) => {
      console.error('Lexical error:', error);
    },
  };

  const handleChange = (editorState: any) => {
    editorState.read(() => {
      const root = $getRoot();
      const textContent = root.getTextContent();
      setContent(textContent);
    });
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Lexical Editor Test</h1>
      
      <div className="border border-gray-300 rounded-lg p-4 mb-4">
        <LexicalComposer initialConfig={initialConfig}>
          <RichTextPlugin
            contentEditable={
              <ContentEditable
                className="min-h-[200px] outline-none p-2 border border-gray-200 rounded"
                aria-placeholder="Type something here..."
                placeholder={
                  <div className="text-gray-400 absolute top-2 left-2 pointer-events-none">
                    Type something here...
                  </div>
                }
              />
            }
            placeholder={
              <div className="text-gray-400 absolute top-2 left-2 pointer-events-none">
                Type something here...
              </div>
            }
            ErrorBoundary={LexicalErrorBoundary}
          />
          <HistoryPlugin />
          <OnChangePlugin onChange={handleChange} />
        </LexicalComposer>
      </div>

      <div className="bg-gray-100 p-4 rounded">
        <h3 className="font-semibold mb-2">Content Output:</h3>
        <pre className="text-sm">{content || 'No content yet...'}</pre>
      </div>

      <div className="mt-4 text-sm text-gray-600">
        <p><strong>Test keyboard shortcuts:</strong></p>
        <ul className="list-disc list-inside">
          <li>Ctrl+B (or Cmd+B) - Bold</li>
          <li>Ctrl+I (or Cmd+I) - Italic</li>
          <li>Ctrl+U (or Cmd+U) - Underline</li>
          <li>Ctrl+Z (or Cmd+Z) - Undo</li>
          <li>Ctrl+Shift+Z (or Cmd+Shift+Z) - Redo</li>
        </ul>
      </div>
    </div>
  );
};

export default LexicalEditorTest;
