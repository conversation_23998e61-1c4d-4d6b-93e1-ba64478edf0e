/**
 * GoogleDocsEditor - Professional rich text editor powered by Lexical
 * Google Docs-style interface with Emma branding and advanced editing features
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  Bold, Italic, Underline, AlignLeft, AlignCenter, AlignRight,
  List, ListOrdered, Link, Type, Image, Wand2, Sparkles,
  MoreHorizontal, Undo, Redo, Printer, Share, Users, MessageCircle,
  History, Star, Folder, ChevronDown, Highlighter, PanelRightOpen, PanelRightClose
} from 'lucide-react';

// Lexical imports
import { $getRoot } from 'lexical';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { LinkPlugin } from '@lexical/react/LexicalLinkPlugin';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import {
  INSERT_ORDERED_LIST_COMMAND,
  INSERT_UNORDERED_LIST_COMMAND,
  ListNode,
  ListItemNode,
} from '@lexical/list';
import { LinkNode } from '@lexical/link';
import { HeadingNode } from '@lexical/rich-text';
import {
  FORMAT_TEXT_COMMAND,
  FORMAT_ELEMENT_COMMAND,
  UNDO_COMMAND,
  REDO_COMMAND,
} from 'lexical';

// Import Lexical editor styles
import './LexicalEditor.css';

interface GoogleDocsEditorProps {
  content: string;
  onChange: (content: string) => void;
  onImageInsert?: () => void;
  projectId: string;
  className?: string;
  documentTitle?: string;
  onTitleChange?: (title: string) => void;
  collaborators?: Array<{
    id: string;
    name: string;
    avatar?: string;
    color: string;
  }>;
  showSidebar?: boolean;
  onToggleSidebar?: () => void;
}

const GoogleDocsEditor: React.FC<GoogleDocsEditorProps> = ({
  content,
  onChange,
  onImageInsert,
  projectId,
  className = '',
  documentTitle = 'Documento sin título',
  onTitleChange,
  collaborators = [],
  showSidebar = true,
  onToggleSidebar
}) => {
  const titleRef = useRef<HTMLInputElement>(null);

  // State management
  const [isConnected, setIsConnected] = useState(false);
  const [currentTitle, setCurrentTitle] = useState(documentTitle);
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [lastSaved] = useState<Date>(new Date());
  const [isSaving] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [zoomLevel, setZoomLevel] = useState(100);

  // Lexical editor configuration
  const initialConfig = {
    namespace: 'EmmaDocsEditor',
    theme: {
      text: {
        bold: 'PlaygroundEditorTheme__bold',
        italic: 'PlaygroundEditorTheme__italic',
        underline: 'PlaygroundEditorTheme__underline',
        strikethrough: 'PlaygroundEditorTheme__strikethrough',
        underlineStrikethrough: 'PlaygroundEditorTheme__underlineStrikethrough',
      },
      paragraph: 'PlaygroundEditorTheme__paragraph',
      heading: {
        h1: 'PlaygroundEditorTheme__h1',
        h2: 'PlaygroundEditorTheme__h2',
        h3: 'PlaygroundEditorTheme__h3',
      },
      list: {
        nested: {
          listitem: 'PlaygroundEditorTheme__listItem',
        },
        ol: 'PlaygroundEditorTheme__ol',
        ul: 'PlaygroundEditorTheme__ul',
        listitem: 'PlaygroundEditorTheme__listItem',
        listitemChecked: 'PlaygroundEditorTheme__listItemChecked',
        listitemUnchecked: 'PlaygroundEditorTheme__listItemUnchecked',
      },
      link: 'PlaygroundEditorTheme__link',
      code: 'PlaygroundEditorTheme__code',
      codeBlock: 'PlaygroundEditorTheme__codeBlock',
      mark: 'PlaygroundEditorTheme__mark',
      markOverlap: 'PlaygroundEditorTheme__markOverlap',
    },
    nodes: [
      HeadingNode,
      ListNode,
      ListItemNode,
      LinkNode,
    ],
    onError: (error: Error) => {
      console.error('Lexical error:', error);
    },
  };

  // Initialize editor
  useEffect(() => {
    setIsConnected(true);
  }, [projectId]);

  // Handle Lexical editor changes
  const handleLexicalChange = useCallback((editorState: any) => {
    editorState.read(() => {
      const root = $getRoot();
      const textContent = root.getTextContent();
      const htmlContent = root.getChildren().map((child: any) => {
        if (child.getType() === 'paragraph') {
          return `<p>${child.getTextContent()}</p>`;
        }
        return child.getTextContent();
      }).join('');

      onChange(htmlContent || textContent);
      updateWordCount(textContent);
    });
  }, [onChange]);

  // Update word count
  const updateWordCount = useCallback((text: string) => {
    const words = text.replace(/<[^>]*>/g, '').trim().split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
  }, []);

  // Auto-save functionality
  useEffect(() => {
    if (content) {
      updateWordCount(content);
    }
  }, [content, updateWordCount]);

  // Lexical Toolbar Component
  const LexicalToolbar = () => {
    const [editor] = useLexicalComposerContext();

    const formatText = (format: 'bold' | 'italic' | 'underline') => {
      editor.dispatchCommand(FORMAT_TEXT_COMMAND, format);
    };

    const formatElement = (format: 'left' | 'center' | 'right' | 'justify') => {
      editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, format);
    };

    const insertList = (listType: 'bullet' | 'number') => {
      if (listType === 'bullet') {
        editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
      } else {
        editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
      }
    };

    const handleUndo = () => {
      editor.dispatchCommand(UNDO_COMMAND, undefined);
    };

    const handleRedo = () => {
      editor.dispatchCommand(REDO_COMMAND, undefined);
    };

    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '1px' }}>
        {/* Undo/Redo */}
        <button
          onClick={handleUndo}
          style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
          onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
          onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
          title="Deshacer"
        >
          <Undo size={14} color="#5f6368" />
        </button>
        <button
          onClick={handleRedo}
          style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
          onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
          onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
          title="Rehacer"
        >
          <Redo size={14} color="#5f6368" />
        </button>

        <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 3px' }} />

        {/* Text Formatting */}
        <button
          onClick={() => formatText('bold')}
          style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
          onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
          onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
          title="Negrita"
        >
          <Bold size={14} color="#5f6368" />
        </button>
        <button
          onClick={() => formatText('italic')}
          style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
          onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
          onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
          title="Cursiva"
        >
          <Italic size={14} color="#5f6368" />
        </button>
        <button
          onClick={() => formatText('underline')}
          style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
          onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
          onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
          title="Subrayado"
        >
          <Underline size={14} color="#5f6368" />
        </button>

        <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 3px' }} />

        {/* Alignment */}
        <button
          onClick={() => formatElement('left')}
          style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
          onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
          onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
          title="Alinear izquierda"
        >
          <AlignLeft size={14} color="#5f6368" />
        </button>
        <button
          onClick={() => formatElement('center')}
          style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
          onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
          onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
          title="Centrar"
        >
          <AlignCenter size={14} color="#5f6368" />
        </button>
        <button
          onClick={() => formatElement('right')}
          style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
          onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
          onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
          title="Alinear derecha"
        >
          <AlignRight size={14} color="#5f6368" />
        </button>

        <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 3px' }} />

        {/* Lists */}
        <button
          onClick={() => insertList('bullet')}
          style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
          onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
          onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
          title="Lista con viñetas"
        >
          <List size={14} color="#5f6368" />
        </button>
        <button
          onClick={() => insertList('number')}
          style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
          onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
          onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
          title="Lista numerada"
        >
          <ListOrdered size={14} color="#5f6368" />
        </button>
      </div>
    );
  };

  return (
    <div className={`google-docs-editor ${className}`} style={{ height: '100vh', display: 'flex', flexDirection: 'column', background: '#f8f9fa' }}>
      {/* Compact Google Docs Header */}
      <div style={{ background: 'white', borderBottom: '1px solid #e8eaed' }}>
        {/* Top Menu Bar - Compact */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '4px 16px', minHeight: '40px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            {/* Emma Logo - Smaller */}
            <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
              <div style={{
                width: '24px',
                height: '24px',
                background: 'linear-gradient(135deg, #3018ef 0%, #dd3a5a 100%)',
                borderRadius: '6px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Sparkles size={12} color="white" />
              </div>
              <span style={{ fontWeight: '600', color: '#202124', fontSize: '14px' }}>Emma Docs</span>
            </div>

            {/* Document Title - Inline */}
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              {isEditingTitle ? (
                <input
                  ref={titleRef}
                  value={currentTitle}
                  onChange={(e) => setCurrentTitle(e.target.value)}
                  onBlur={() => {
                    setIsEditingTitle(false);
                    onTitleChange?.(currentTitle);
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      setIsEditingTitle(false);
                      onTitleChange?.(currentTitle);
                    }
                  }}
                  style={{
                    fontSize: '14px',
                    fontWeight: '400',
                    color: '#202124',
                    background: 'transparent',
                    border: 'none',
                    borderBottom: '1px solid #3018ef',
                    outline: 'none',
                    padding: '2px 4px',
                    minWidth: '200px'
                  }}
                  autoFocus
                />
              ) : (
                <span
                  onClick={() => setIsEditingTitle(true)}
                  style={{
                    fontSize: '14px',
                    fontWeight: '400',
                    color: '#202124',
                    cursor: 'pointer',
                    padding: '2px 4px',
                    borderRadius: '3px',
                    transition: 'background 0.2s'
                  }}
                  onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
                  onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
                >
                  {currentTitle}
                </span>
              )}
              <Star size={14} style={{ color: '#9aa0a6', cursor: 'pointer' }} />
            </div>
          </div>

          {/* Right Side Actions - Compact */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
            {/* Collaborators */}
            {collaborators.length > 0 && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
                {collaborators.slice(0, 2).map((collaborator) => (
                  <div
                    key={collaborator.id}
                    style={{
                      width: '24px',
                      height: '24px',
                      borderRadius: '50%',
                      background: collaborator.color,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '10px',
                      fontWeight: '500'
                    }}
                    title={collaborator.name}
                  >
                    {collaborator.name.charAt(0).toUpperCase()}
                  </div>
                ))}
              </div>
            )}

            {/* Compact Share Button */}
            <button style={{
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              padding: '6px 12px',
              border: 'none',
              borderRadius: '4px',
              background: 'linear-gradient(135deg, #3018ef 0%, #dd3a5a 100%)',
              color: 'white',
              cursor: 'pointer',
              fontSize: '12px',
              fontWeight: '500'
            }}>
              <Share size={14} />
              <span>Compartir</span>
            </button>
          </div>
        </div>

        {/* Compact Menu Navigation */}
        <div style={{ padding: '0 16px', borderTop: '1px solid #e8eaed' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            {['Archivo', 'Editar', 'Ver', 'Insertar', 'Formato', 'Herramientas', 'Emma AI'].map((item) => (
              <button
                key={item}
                style={{
                  padding: '6px 8px',
                  border: 'none',
                  background: 'transparent',
                  color: '#5f6368',
                  fontSize: '13px',
                  cursor: 'pointer',
                  borderRadius: '3px'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = '#f1f3f4';
                  e.currentTarget.style.color = '#202124';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'transparent';
                  e.currentTarget.style.color = '#5f6368';
                }}
              >
                {item}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Compact Google Docs Toolbar */}
      <div style={{ background: 'white', borderBottom: '1px solid #e8eaed', padding: '4px 16px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '6px', flexWrap: 'wrap' }}>
          {/* Print Button */}
          <button
            onClick={() => window.print()}
            style={{ width: '28px', height: '28px', border: 'none', borderRadius: '3px', background: 'transparent', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
            onMouseEnter={(e) => e.currentTarget.style.background = '#f1f3f4'}
            onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
            title="Imprimir"
          >
            <Printer size={14} color="#5f6368" />
          </button>

          <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 3px' }} />

          {/* Zoom */}
          <select
            value={zoomLevel}
            onChange={(e) => setZoomLevel(Number(e.target.value))}
            style={{
              border: '1px solid #dadce0',
              borderRadius: '3px',
              padding: '2px 6px',
              fontSize: '12px',
              background: '#ffffff',
              color: '#202124',
              minWidth: '60px',
              opacity: 1
            }}
          >
            <option value={75}>75%</option>
            <option value={100}>100%</option>
            <option value={125}>125%</option>
            <option value={150}>150%</option>
          </select>

          <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 3px' }} />

          {/* Font Controls - Placeholder for future implementation */}
          <select
            onChange={(_e) => {/* Font family will be implemented with Lexical commands */}}
            style={{
              border: '1px solid #dadce0',
              borderRadius: '3px',
              padding: '2px 6px',
              fontSize: '12px',
              background: '#ffffff',
              color: '#202124',
              minWidth: '100px',
              opacity: 1
            }}
          >
            <option value="Arial">Arial</option>
            <option value="Calibri">Calibri</option>
            <option value="Times New Roman">Times New Roman</option>
            <option value="Georgia">Georgia</option>
          </select>

          <select
            onChange={(_e) => {/* Font size will be implemented with Lexical commands */}}
            style={{
              border: '1px solid #dadce0',
              borderRadius: '3px',
              padding: '2px 6px',
              fontSize: '12px',
              background: '#ffffff',
              color: '#202124',
              minWidth: '50px',
              opacity: 1
            }}
          >
            <option value="1">8</option>
            <option value="2">10</option>
            <option value="3">12</option>
            <option value="4">14</option>
            <option value="5">18</option>
            <option value="6">24</option>
            <option value="7">36</option>
          </select>

          <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 3px' }} />

          {/* Formatting Buttons - Will be replaced with Lexical toolbar */}
          <LexicalToolbar />

          <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 3px' }} />

          {/* Note: Alignment and Lists are now handled by LexicalToolbar */}

          <div style={{ width: '1px', height: '20px', background: '#e8eaed', margin: '0 3px' }} />

          {/* Sidebar Toggle Button - Bigger and More Visible */}
          {onToggleSidebar && (
            <button
              onClick={onToggleSidebar}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                padding: '6px 12px',
                border: '1px solid #dadce0',
                borderRadius: '4px',
                background: showSidebar ? '#e8f0fe' : '#ffffff',
                color: showSidebar ? '#1a73e8' : '#5f6368',
                cursor: 'pointer',
                fontSize: '13px',
                fontWeight: '500',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = showSidebar ? '#d2e3fc' : '#f8f9fa';
                e.currentTarget.style.borderColor = '#1a73e8';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = showSidebar ? '#e8f0fe' : '#ffffff';
                e.currentTarget.style.borderColor = '#dadce0';
              }}
              title={showSidebar ? 'Ocultar Emma SEO Intelligence' : 'Mostrar Emma SEO Intelligence'}
            >
              {showSidebar ? <PanelRightClose size={16} /> : <PanelRightOpen size={16} />}
              <span>{showSidebar ? 'Ocultar SEO' : 'Emma SEO'}</span>
            </button>
          )}

          {/* Emma AI Button */}
          {onImageInsert && (
            <button
              onClick={onImageInsert}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '3px',
                padding: '4px 8px',
                border: 'none',
                borderRadius: '4px',
                background: 'linear-gradient(135deg, #3018ef 0%, #dd3a5a 100%)',
                color: 'white',
                cursor: 'pointer',
                fontSize: '12px',
                fontWeight: '500'
              }}
              title="Generar imagen con Emma AI"
            >
              <Image size={12} />
              <Wand2 size={10} />
              <span>Emma AI</span>
            </button>
          )}
        </div>
      </div>

      {/* Document Area with Lexical Editor */}
      <div style={{ flex: 1, overflow: 'auto', background: '#f8f9fa', padding: '20px', display: 'flex', justifyContent: 'center' }}>
        <div style={{ transform: `scale(${zoomLevel / 100})`, transformOrigin: 'top center' }}>
          <div
            className="lexical-editor"
            style={{
              width: '8.5in',
              minHeight: '11in',
              background: 'white',
              boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
              borderRadius: '2px',
              padding: '1in',
              fontFamily: 'Arial, sans-serif',
              fontSize: '11pt',
              lineHeight: '1.5',
              color: '#202124'
            }}
          >
            <LexicalComposer initialConfig={initialConfig}>
              <RichTextPlugin
                contentEditable={
                  <ContentEditable
                    style={{
                      outline: 'none',
                      minHeight: '9in',
                      width: '100%',
                      resize: 'none',
                      fontSize: '11pt',
                      lineHeight: '1.5',
                      fontFamily: 'Arial, sans-serif'
                    }}
                    aria-placeholder="Comienza a escribir tu documento..."
                    placeholder={
                      <div style={{
                        color: '#9aa0a6',
                        position: 'absolute',
                        top: '0',
                        pointerEvents: 'none',
                        userSelect: 'none'
                      }}>
                        Comienza a escribir tu documento...
                      </div>
                    }
                  />
                }
                placeholder={
                  <div style={{
                    color: '#9aa0a6',
                    position: 'absolute',
                    top: '0',
                    pointerEvents: 'none',
                    userSelect: 'none'
                  }}>
                    Comienza a escribir tu documento...
                  </div>
                }
                ErrorBoundary={LexicalErrorBoundary}
              />
              <HistoryPlugin />
              <ListPlugin />
              <LinkPlugin />
              <OnChangePlugin onChange={handleLexicalChange} />
            </LexicalComposer>
          </div>
        </div>
      </div>

      {/* Status Bar */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        padding: '8px 16px', 
        background: 'white', 
        borderTop: '1px solid #e8eaed', 
        fontSize: '13px', 
        color: '#5f6368' 
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          {/* Save Status */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {isSaving ? (
              <>
                <div style={{ width: '12px', height: '12px', border: '2px solid #3018ef', borderTop: '2px solid transparent', borderRadius: '50%', animation: 'spin 1s linear infinite' }} />
                <span>Guardando...</span>
              </>
            ) : (
              <>
                <div style={{ width: '8px', height: '8px', background: '#34a853', borderRadius: '50%' }} />
                <span>Guardado {lastSaved.toLocaleTimeString()}</span>
              </>
            )}
          </div>

          <span>{wordCount} palabras</span>

          {isConnected ? (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <div style={{ width: '8px', height: '8px', background: '#34a853', borderRadius: '50%' }} />
              <span style={{ color: '#34a853' }}>Colaboración activa</span>
            </div>
          ) : (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <div style={{ width: '8px', height: '8px', background: '#ea4335', borderRadius: '50%' }} />
              <span style={{ color: '#ea4335' }}>Sin conexión</span>
            </div>
          )}
        </div>

        {/* Emma AI Indicator */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Sparkles size={14} color="#3018ef" />
          <span style={{ 
            background: 'linear-gradient(135deg, #3018ef 0%, #dd3a5a 100%)', 
            WebkitBackgroundClip: 'text', 
            WebkitTextFillColor: 'transparent',
            fontWeight: '500'
          }}>
            Emma AI
          </span>
        </div>
      </div>
    </div>
  );
};

export default GoogleDocsEditor;
export { GoogleDocsEditor as EtherpadEditor };
