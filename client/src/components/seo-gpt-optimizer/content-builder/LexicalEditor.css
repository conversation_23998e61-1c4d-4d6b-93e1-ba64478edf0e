/**
 * Lexical Editor Styles for Emma Content Builder
 * Professional Google Docs-style editor styling
 */

/* Editor Container */
.lexical-editor {
  position: relative;
  font-family: 'Arial', sans-serif;
  font-size: 11pt;
  line-height: 1.5;
  color: #202124;
}

/* Content Editable Area */
.lexical-editor .ContentEditable__root {
  outline: none;
  border: none;
  resize: none;
  cursor: text;
  display: block;
  position: relative;
  tab-size: 1;
  user-select: text;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Placeholder */
.lexical-editor .ContentEditable__placeholder {
  color: #9aa0a6;
  overflow: hidden;
  position: absolute;
  text-overflow: ellipsis;
  top: 0;
  left: 0;
  font-size: 11pt;
  user-select: none;
  display: inline-block;
  pointer-events: none;
}

/* Text Formatting */
.lexical-editor .PlaygroundEditorTheme__bold {
  font-weight: bold;
}

.lexical-editor .PlaygroundEditorTheme__italic {
  font-style: italic;
}

.lexical-editor .PlaygroundEditorTheme__underline {
  text-decoration: underline;
}

.lexical-editor .PlaygroundEditorTheme__strikethrough {
  text-decoration: line-through;
}

.lexical-editor .PlaygroundEditorTheme__underlineStrikethrough {
  text-decoration: underline line-through;
}

/* Headings */
.lexical-editor .PlaygroundEditorTheme__h1 {
  font-size: 24px;
  color: #202124;
  font-weight: 400;
  margin: 0;
  margin-bottom: 12px;
  padding: 0;
}

.lexical-editor .PlaygroundEditorTheme__h2 {
  font-size: 18px;
  color: #202124;
  font-weight: 400;
  margin: 0;
  margin-top: 20px;
  margin-bottom: 6px;
  padding: 0;
}

.lexical-editor .PlaygroundEditorTheme__h3 {
  font-size: 14px;
  color: #202124;
  font-weight: 400;
  margin: 0;
  margin-top: 16px;
  margin-bottom: 4px;
  padding: 0;
}

/* Paragraphs */
.lexical-editor .PlaygroundEditorTheme__paragraph {
  margin: 0;
  margin-bottom: 8px;
  position: relative;
}

.lexical-editor .PlaygroundEditorTheme__paragraph:last-child {
  margin-bottom: 0;
}

/* Lists */
.lexical-editor .PlaygroundEditorTheme__ul {
  padding: 0;
  margin: 0;
  margin-left: 16px;
  list-style-position: inside;
}

.lexical-editor .PlaygroundEditorTheme__ol {
  padding: 0;
  margin: 0;
  margin-left: 16px;
  list-style-position: inside;
}

.lexical-editor .PlaygroundEditorTheme__listItem {
  margin: 8px 32px 8px 32px;
}

.lexical-editor .PlaygroundEditorTheme__listItemChecked,
.lexical-editor .PlaygroundEditorTheme__listItemUnchecked {
  position: relative;
  margin-left: 8px;
  margin-right: 8px;
  padding-left: 24px;
  padding-right: 24px;
  list-style-type: none;
  outline: none;
}

.lexical-editor .PlaygroundEditorTheme__listItemChecked {
  text-decoration: line-through;
}

.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before,
.lexical-editor .PlaygroundEditorTheme__listItemChecked:before {
  content: '';
  width: 16px;
  height: 16px;
  top: 2px;
  left: 0;
  cursor: pointer;
  display: block;
  background-size: cover;
  position: absolute;
}

.lexical-editor .PlaygroundEditorTheme__listItemUnchecked[dir='rtl']:before,
.lexical-editor .PlaygroundEditorTheme__listItemChecked[dir='rtl']:before {
  left: auto;
  right: 0;
}

.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:focus:before,
.lexical-editor .PlaygroundEditorTheme__listItemChecked:focus:before {
  box-shadow: 0 0 0 2px #a6cdfe;
  border-radius: 2px;
}

.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before {
  border: 1px solid #999;
  border-radius: 2px;
}

.lexical-editor .PlaygroundEditorTheme__listItemChecked:before {
  border: 1px solid rgb(61, 135, 245);
  border-radius: 2px;
  background-color: #3d87f5;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg width='10' height='8' viewBox='0 0 10 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M9 1L4 6L1 3' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
}

/* Links */
.lexical-editor .PlaygroundEditorTheme__link {
  color: #3d87f5;
  text-decoration: none;
}

.lexical-editor .PlaygroundEditorTheme__link:hover {
  text-decoration: underline;
  cursor: pointer;
}

/* Text Alignment */
.lexical-editor .PlaygroundEditorTheme__textLeft {
  text-align: left;
}

.lexical-editor .PlaygroundEditorTheme__textCenter {
  text-align: center;
}

.lexical-editor .PlaygroundEditorTheme__textRight {
  text-align: right;
}

.lexical-editor .PlaygroundEditorTheme__textJustify {
  text-align: justify;
}

/* Code */
.lexical-editor .PlaygroundEditorTheme__code {
  background-color: rgb(240, 242, 245);
  padding: 1px 0.25rem;
  font-family: Menlo, Consolas, Monaco, monospace;
  font-size: 94%;
}

.lexical-editor .PlaygroundEditorTheme__codeBlock {
  position: relative;
  margin: 8px 0;
  background-color: rgb(240, 242, 245);
  padding: 8px 8px 8px 52px;
  border-radius: 8px;
  overflow-x: auto;
  font-family: Menlo, Consolas, Monaco, monospace;
  font-size: 13px;
  line-height: 1.53;
  tab-size: 2;
}

/* Selection */
.lexical-editor .PlaygroundEditorTheme__mark {
  background: rgba(255, 212, 0, 0.14);
  border-bottom: 2px solid rgba(255, 212, 0, 0.3);
  padding-bottom: 2px;
}

.lexical-editor .PlaygroundEditorTheme__markOverlap {
  background: rgba(255, 212, 0, 0.3);
  border-bottom: 2px solid rgba(255, 212, 0, 0.7);
}

/* Focus */
.lexical-editor:focus-within {
  outline: none;
}

/* Emma Branding */
.lexical-editor .emma-highlight {
  background: linear-gradient(135deg, rgba(48, 24, 239, 0.1) 0%, rgba(221, 58, 90, 0.1) 100%);
  border-radius: 3px;
  padding: 2px 4px;
}

.lexical-editor .emma-suggestion {
  border-bottom: 2px dotted #3018ef;
  cursor: pointer;
}

.lexical-editor .emma-suggestion:hover {
  background: rgba(48, 24, 239, 0.05);
}
